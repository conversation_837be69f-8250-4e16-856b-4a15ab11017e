"""
Test the simplified SQL toolkit implementation.
"""

import sys
import os
from unittest.mock import Mock

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from tools.schema_less_sql_toolkit import create_sql_conversion_agent
    from state.state import ErrorContext
    
    print("✅ Import successful!")
    
    # Mock LLM that mimics your AzureOpenAI structure
    class MockLLM:
        def __init__(self):
            self.client = Mock()
        
        def invoke(self, prompt):
            # Mock response with JSON format
            class MockResponse:
                def __init__(self):
                    self.content = '''
                    {
                        "corrected_statement": "WHERE id = 123",
                        "explanation": "Removed quotes from integer comparison for PostgreSQL compatibility",
                        "changes_made": "Changed string '123' to integer 123"
                    }
                    '''
            return MockResponse()
    
    mock_llm = MockLLM()
    
    # Create test contexts
    source_context = ErrorContext(
        before_statement="SELECT * FROM test_table",
        before_statement_number=1,
        error_statement="WHERE id = '123'",
        error_statement_number=2,
        after_statement="ORDER BY name",
        after_statement_number=3
    )
    
    target_context = ErrorContext(
        before_statement="SELECT * FROM test_table",
        before_statement_number=1,
        error_statement="WHERE id = '123'",
        error_statement_number=2,
        after_statement="ORDER BY name",
        after_statement_number=3
    )
    
    target_statements = [
        "SELECT * FROM test_table",
        "WHERE id = '123'",
        "ORDER BY name"
    ]
    
    error_message = "ERROR: operator does not exist: integer = character varying"
    
    print("🔧 Testing simplified SQL toolkit agent...")
    
    result = create_sql_conversion_agent(
        mock_llm, source_context, target_context, 
        target_statements, error_message
    )
    
    if result:
        print("✅ Simplified SQL Toolkit agent worked!")
        print(f"   Strategy: {result.get('conversion_strategy')}")
        print(f"   Corrections: {len(result.get('ai_corrections', []))}")
        if result.get('ai_corrections'):
            correction = result['ai_corrections'][0]
            print(f"   Original: {correction.get('original_statement')}")
            print(f"   Corrected: {correction.get('corrected_statement')}")
    else:
        print("❌ Simplified SQL Toolkit agent failed")
    
    print("\n🎯 Simplified Implementation Test Complete!")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
