"""
Test file for dual conversion strategies.
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.schema_less_sql_toolkit import SchemaLessSQLToolkit
from strategies.conversion_strategy_manager import ConversionStrategyManager, ConversionStrategy
from state.state import ErrorContext

class TestDualConversion(unittest.TestCase):
    """Test cases for dual conversion strategies."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.mock_llm = Mock()
        self.mock_llm.client = Mock()
        
        # Create test contexts
        self.source_context = ErrorContext(
            before_statement="SELECT * FROM test_table",
            before_statement_number=1,
            error_statement="WHERE id = '123'",
            error_statement_number=2,
            after_statement="ORDER BY name",
            after_statement_number=3
        )
        
        self.target_error_context = ErrorContext(
            before_statement="SELECT * FROM test_table",
            before_statement_number=1,
            error_statement="WHERE id = '123'",  # This will cause type error in PostgreSQL
            error_statement_number=2,
            after_statement="ORDER BY name",
            after_statement_number=3
        )
        
        self.target_statements = [
            "SELECT * FROM test_table",
            "WHERE id = '123'",
            "ORDER BY name"
        ]
        
        self.error_message = "ERROR: operator does not exist: integer = character varying"
    
    def test_sql_toolkit_initialization(self):
        """Test SQL toolkit initialization."""
        toolkit = SchemaLessSQLToolkit(self.mock_llm)
        self.assertIsNotNone(toolkit)
        self.assertEqual(toolkit.llm, self.mock_llm)
    
    def test_strategy_manager_initialization(self):
        """Test strategy manager initialization."""
        mock_ai_converter = Mock()
        mock_sql_toolkit = Mock()
        
        manager = ConversionStrategyManager(
            llm=self.mock_llm,
            ai_converter=mock_ai_converter,
            sql_toolkit=mock_sql_toolkit
        )
        
        self.assertIsNotNone(manager)
        self.assertEqual(manager.llm, self.mock_llm)
        self.assertEqual(manager.ai_converter, mock_ai_converter)
        self.assertEqual(manager.sql_toolkit, mock_sql_toolkit)
    
    def test_strategy_selection_target_specific(self):
        """Test strategy selection for target-specific errors."""
        mock_ai_converter = Mock()
        mock_sql_toolkit = Mock()
        
        manager = ConversionStrategyManager(
            llm=self.mock_llm,
            ai_converter=mock_ai_converter,
            sql_toolkit=mock_sql_toolkit
        )
        
        # Create target-specific context (no source mapping)
        target_specific_context = ErrorContext(
            before_statement="",
            before_statement_number=0,
            error_statement="",
            error_statement_number=0,
            after_statement="",
            after_statement_number=0
        )
        
        strategy = manager.select_conversion_strategy(
            target_specific_context,
            self.target_error_context,
            self.error_message
        )
        
        self.assertEqual(strategy, ConversionStrategy.SQL_TOOLKIT)
    
    def test_strategy_selection_ai_conversion(self):
        """Test strategy selection for AI conversion."""
        mock_ai_converter = Mock()
        mock_sql_toolkit = Mock()
        
        manager = ConversionStrategyManager(
            llm=self.mock_llm,
            ai_converter=mock_ai_converter,
            sql_toolkit=mock_sql_toolkit
        )
        
        strategy = manager.select_conversion_strategy(
            self.source_context,
            self.target_error_context,
            "General conversion error"
        )
        
        self.assertEqual(strategy, ConversionStrategy.AI_CONVERSION)
    
    @patch('tools.schema_less_sql_toolkit.StatementConversionOutput')
    def test_sql_toolkit_conversion(self, mock_output):
        """Test SQL toolkit conversion."""
        # Mock the structured output
        mock_result = Mock()
        mock_result.corrected_statements = [
            Mock(
                statement_number=2,
                original_statement="WHERE id = '123'",
                corrected_statement="WHERE id = 123",
                statement_type="error_statement",
                changes_made="Removed quotes from integer comparison"
            )
        ]
        mock_result.explanation = "Fixed type casting issue"
        
        self.mock_llm.client.with_structured_output.return_value.invoke.return_value = mock_result
        
        toolkit = SchemaLessSQLToolkit(self.mock_llm)
        result = toolkit.convert_with_toolkit(
            self.source_context,
            self.target_error_context,
            self.target_statements,
            self.error_message
        )
        
        self.assertIsNotNone(result)
        self.assertIn("ai_corrections", result)
        self.assertEqual(result["conversion_strategy"], "sql_toolkit")
    
    def test_strategy_manager_conversion(self):
        """Test strategy manager conversion execution."""
        mock_ai_converter = Mock()
        mock_ai_converter.return_value = {
            "ai_corrections": [{"statement_number": 2, "corrected_statement": "WHERE id = 123"}],
            "explanation": "AI conversion result"
        }
        
        mock_sql_toolkit = Mock()
        mock_sql_toolkit.convert_with_toolkit.return_value = {
            "ai_corrections": [{"statement_number": 2, "corrected_statement": "WHERE id = 123"}],
            "explanation": "SQL toolkit conversion result",
            "conversion_strategy": "sql_toolkit"
        }
        
        manager = ConversionStrategyManager(
            llm=self.mock_llm,
            ai_converter=mock_ai_converter,
            sql_toolkit=mock_sql_toolkit
        )
        
        result = manager.convert_with_strategy_selection(
            self.source_context,
            self.target_error_context,
            self.target_statements,
            self.error_message
        )
        
        self.assertIsNotNone(result)
        self.assertIn("ai_corrections", result)
        self.assertIn("conversion_strategy", result)

if __name__ == '__main__':
    unittest.main()
