SET search_path TO LAB;
CREATE OR REPLACE PROCEDURE lab.P_ADDRESULTSFROMEQUIPMENT10T ()
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    V_LABREPORTID LABREPORTS.Labreportid%TYPE;
BEGIN
    -- Procedure logic here
END;$$;
COUNTER integer;
--A VARCHAR2(50);
    --j NUMBER:=0;
    LV_PARAM varchar(50);
-- CURSORS DECLARATION
    RESULTS CURSOR FOR SELECT DISTINCT
            ER.TRA_LINK_SAMPLEREQ_ID,
            EL.MST_EQUIPMENT_ID,
            TRIM(EL.SAMPLEID) SAMPLEID
        FROM
            lab.EI_TRA_RESHEADER ER,
            EI_TRA_LINK_SAMPLEREQ EL
        WHERE
            ER.PROCESSED_STATUS = '1'
            AND ER.TRA_LINK_SAMPLEREQ_ID = EL.TRA_LINK_SAMPLEREQ_ID
        LIMIT 599;
RESULT1 CURSOR (EQSID varchar)
    FOR SELECT DISTINCT
            RTC.requesttestid
        FROM
            lab.requesttests RTC
        WHERE
            RTC.SIN = LV_PARAM
            AND RTC.SAMPLECOLLECTED = 'Y'
            AND RTC.PREPARESAMPLE = 'Y'
            AND RTC.Sampleverify = 'N'
            AND RTC.TYPEOFPROCESSING IN (323, 322);
-- Variable daclaration for Result1 Cursor
    -- -- --TYPE lt_requesttestid is table of lab.REQUESTTESTS.REQUESTTESTID%TYPE INDEX BY integer;
    --lv_requesttestid lt_requesttestid
    lv_requesttestid varchar[];
-- Record Type Creation for Results Cusor
    record_results record;
rv_mst_equipment_id lab.EI_TRA_LINK_SAMPLEREQ.MST_EQUIPMENT_ID%TYPE;
rv_tra_link_samplereq_id lab.EI_TRA_RESHEADER.TRA_LINK_SAMPLEREQ_ID%TYPE;
rv_sampleid lab.EI_TRA_LINK_SAMPLEREQ.SAMPLEID%TYPE;
-- ----TYPE tb_record_results is table of record_results INDEX BY integer;
    var_record_results varchar[];
-- Execution Block Begins
    ora2pg_rowcount int := 0;
REC_COUNT record;
RESULT_COUNT record;
BEGIN
    SET search_path TO LAB;
OPEN RESULTS;
FETCH RESULTS INTO var_record_results;
-- FORALL I IN var_record_results.FIRST .. var_record_results.LAST
    -- Iterating The Collection Variable for Results Cursor Begins
    FOR REC_COUNT IN 1..var_record_results.count LOOP
        BEGIN
            LV_PARAM := var_record_results[REC_COUNT].rv_sampleid;
BEGIN
                OPEN RESULT1 (LV_PARAM);
FETCH RESULT1 INTO lv_requesttestid;
IF array_length(lv_requesttestid, 1) IS NULL OR array_length(lv_requesttestid, 1) = 0 THEN
UPDATE
                                lab.LABREPORTS LR
                            SET
                                UPDATEDDATE = current_timestamp(0)::timestamp
                            WHERE
                                LR.LABREPORTID = V_LABREPORTID;
BEGIN
    PERFORM nextval('lab.S_LABREPORTID') INTO V_LABREPORTID;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'An error occurred: %', sqlerrm;
END;
INSERT INTO LABREPORTS (LABREPORTID, REQUESTTESTID, REPRINT, DISPATCH, NOOFORGINALS, NOOFDUPLICATES, DISPATCHON, REPORTVERIFY, REPORTPRINTDATE, DUPLICATEREPORTPRINTDATE, STATUS, CREATEDBY, CREATEDDATE, UPDATEDBY, UPDATEDDATE, GROUPNAME)
                            VALUES (V_LABREPORTID, lv_requesttestid[RESULT_COUNT], NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, current_timestamp(0)::timestamp, NULL, NULL, NULL);
END;
UPDATE
                            lab.TESTREPORTS B
                        SET
                            UPDATEDDATE = current_timestamp(0)::timestamp
                        WHERE
                            LABREPORTID = V_LABREPORTID;
GET DIAGNOSTICS ora2pg_rowcount = row_count;
INSERT INTO TESTREPORTS (PARAMETERID, PARAMETERDETAILID, RESULT, REPORTSTATUS, STATUS, CREATEDBY, CREATEDDATE, COMMENTS, UPDATEDBY, UPDATEDDATE, LABREPORTID, TRA_LINK_SAMPLEREQ_ID, NUMBEROFREPEATS) SELECT tp.parameterid, LLP.PARAMETERDETAILID, ( CASE WHEN abc.mst_equipment_id = 36 THEN eta.param_result_flag ELSE ETA.EQP_PARAM_RESULT END), NULL, 1, NULL, current_timestamp(0)::timestamp, NULL, NULL, NULL, V_LABREPORTID, var_record_results[REC_COUNT].rv_tra_link_samplereq_id, NULL FROM lab.requesttests RT INNER JOIN lab.testmaster TM ON RT.testid = TM.serviceid INNER JOIN lab.testparameter TP ON TM.serviceid = tp.testid INNER JOIN lab.PARAMETERMASTERMAINDETAIL PM ON TP.PARAMETERID = PM.PARAMETERID INNER JOIN lab.lab_link_param LLP ON llp.parameterdetailid = PM.parameterdetailid INNER JOIN lab.ei_mst_link_param EI ON ei.mst_link_param_id = llp.mst_link_param_id INNER JOIN lab.ei_tra_resheader ETA ON ei.eqp_param_cd = ETA.EQP_PARAM_CD INNER JOIN lab.ei_tra_link_samplereq abc ON abc.tra_link_samplereq_id = eta.tra_link_samplereq_id WHERE (RT.requesttestid = lv_requesttestid[RESULT_COUNT]) AND trim(abc.sampleid)::numeric = trim(var_record_results[REC_COUNT].rv_sampleid) AND TM.STATUS = 1 AND EI.MST_EQUIPMENT_ID = var_record_results[REC_COUNT].rv_mst_equipment_id::numeric AND RT.TESTSTATUS = 1 AND ETA.REPEAT_FLAG::numeric = 'R';
--=================================================================================
                                    GET DIAGNOSTICS ora2pg_rowcount = row_count;
IF ora2pg_rowcount = 0 THEN
INSERT INTO TESTREPORTS (PARAMETERID, PARAMETERDETAILID, RESULT, REPORTSTATUS, STATUS, CREATEDBY, CREATEDDATE, COMMENTS, UPDATEDBY, UPDATEDDATE, LABREPORTID, TRA_LINK_SAMPLEREQ_ID, NUMBEROFREPEATS)
SELECT DISTINCT
tp.parameterid,
LLP.PARAMETERDETAILID,
(CASE WHEN abc.mst_equipment_id = 36 THEN
eta.param_result_flag
ELSE
ETA.EQP_PARAM_RESULT
END),
NULL,
1,
NULL,
current_timestamp(0)::timestamp,
NULL,
NULL,
NULL,
V_LABREPORTID,
var_record_results[REC_COUNT].rv_tra_link_samplereq_id,
NULL
FROM
lab.requesttests RT
INNER JOIN lab.testmaster TM ON RT.testid = TM.serviceid
INNER JOIN lab.testparameter TP ON TM.serviceid = tp.testid
INNER JOIN lab.PARAMETERMASTERMAINDETAIL PM ON TP.PARAMETERID = PM.PARAMETERID
INNER JOIN lab.lab_link_param LLP ON llp.parameterdetailid = PM.parameterdetailid
INNER JOIN lab.ei_mst_link_param EI ON ei.mst_link_param_id = llp.mst_link_param_id
INNER JOIN lab.ei_tra_resheader ETA ON ei.eqp_param_cd = ETA.EQP_PARAM_CD
INNER JOIN lab.ei_tra_link_samplereq abc ON abc.tra_link_samplereq_id = eta.tra_link_samplereq_id
WHERE (RT.requesttestid = lv_requesttestid[RESULT_COUNT])
AND trim(abc.sampleid)::numeric = trim(var_record_results[REC_COUNT].rv_sampleid)
AND TM.STATUS = 1
AND EI.MST_EQUIPMENT_ID = var_record_results[REC_COUNT].rv_mst_equipment_id::numeric
AND RT.TESTSTATUS = 1
AND ETA.REPEAT_FLAG::numeric = 'N'
GROUP BY
tp.parameterid,
LLP.PARAMETERDETAILID,
abc.mst_equipment_id,
eta.param_result_flag,
ETA.EQP_PARAM_RESULT;
END IF;
UPDATE
                                            lab.EI_TRA_RESHEADER
                                        SET
                                            PROCESSED_STATUS = '2'
                                        WHERE
                                            TRA_LINK_SAMPLEREQ_ID = var_record_results[REC_COUNT].rv_tra_link_samplereq_id;
SELECT COUNT(L.REQUESTTESTID) INTO COUNTER FROM lab.TESTREPORTS T INNER JOIN lab.LABREPORTS L ON T.LABREPORTID = L.LABREPORTID WHERE L.REQUESTTESTID = lv_requesttestid[RESULT_COUNT];
IF (COUNTER <> 0) THEN
                                                    UPDATE
                                                        lab.REQUESTTESTS T
                                                    SET
                                                        SAMPLEVERIFY = 'E',
                                                        PROCESSING = 'Y',
                                                        Samplecollected = 'Y',
                                                        preparesample = 'Y',
                                                        updateddate = current_timestamp(0)::timestamp,
                                                        sampleprocesstime = current_timestamp(0)::timestamp,
                                                        EQUIPMENTID = var_record_results[REC_COUNT].rv_mst_equipment_id
                                                    WHERE
                                                        T.REQUESTTESTID = lv_requesttestid[RESULT_COUNT];
END IF;
--
                                                    --
                                END IF;
END LOOP;
-- Results1 Loop End
                END IF;
-- End If for Request Test id is null
            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    UPDATE
                        lab.EI_TRA_RESHEADER
                    SET
                        PROCESSED_STATUS = '3'
                    WHERE
                        TRA_LINK_SAMPLEREQ_ID = var_record_results[REC_COUNT].rv_tra_link_samplereq_id;
END;
-- Results1 Block End
            CLOSE RESULT1;
EXCEPTION
            WHEN NO_DATA_FOUND THEN
                RAISE NOTICE '% %', sqlstate, sqlerrm;
UPDATE
                    lab.EI_TRA_RESHEADER
                SET
                    PROCESSED_STATUS = '3'
                WHERE
                    TRA_LINK_SAMPLEREQ_ID = var_record_results[REC_COUNT].rv_tra_link_samplereq_id;
END;
-- Results Cursors End Block
END LOOP;
-- Results Cursor Loop End
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '% %', sqlstate, sqlerrm;
END;