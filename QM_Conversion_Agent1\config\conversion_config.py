"""
Configuration for dual conversion strategies.
"""

from enum import Enum
from typing import Dict, Any

class ConversionMode(Enum):
    """Conversion mode configuration."""
    AI_ONLY = "ai_only"
    SQL_TOOLKIT_ONLY = "sql_toolkit_only"
    AUTO_SELECT = "auto_select"
    HYBRID = "hybrid"  # Future enhancement

class ConversionConfig:
    """Configuration for conversion strategies."""
    
    # Default configuration
    DEFAULT_CONFIG = {
        "mode": ConversionMode.AUTO_SELECT,
        "enable_fallback": True,
        "ai_strategy": {
            "enabled": True,
            "priority": 1
        },
        "sql_toolkit_strategy": {
            "enabled": True,
            "priority": 2
        },
        "strategy_selection": {
            "target_specific_threshold": 0.8,
            "sql_error_keywords": [
                "syntax error",
                "function does not exist",
                "operator does not exist",
                "type does not exist",
                "column does not exist",
                "relation does not exist",
                "permission denied",
                "invalid input syntax"
            ]
        },
        "logging": {
            "log_strategy_selection": True,
            "log_conversion_details": True,
            "save_strategy_results": True
        }
    }
    
    @classmethod
    def get_config(cls) -> Dict[str, Any]:
        """Get the current configuration."""
        return cls.DEFAULT_CONFIG.copy()
    
    @classmethod
    def update_config(cls, updates: Dict[str, Any]) -> None:
        """Update configuration with new values."""
        cls._deep_update(cls.DEFAULT_CONFIG, updates)
    
    @classmethod
    def _deep_update(cls, base_dict: Dict, update_dict: Dict) -> None:
        """Deep update dictionary."""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                cls._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    @classmethod
    def is_ai_enabled(cls) -> bool:
        """Check if AI strategy is enabled."""
        return cls.DEFAULT_CONFIG["ai_strategy"]["enabled"]
    
    @classmethod
    def is_sql_toolkit_enabled(cls) -> bool:
        """Check if SQL toolkit strategy is enabled."""
        return cls.DEFAULT_CONFIG["sql_toolkit_strategy"]["enabled"]
    
    @classmethod
    def get_mode(cls) -> ConversionMode:
        """Get the current conversion mode."""
        return cls.DEFAULT_CONFIG["mode"]
    
    @classmethod
    def set_mode(cls, mode: ConversionMode) -> None:
        """Set the conversion mode."""
        cls.DEFAULT_CONFIG["mode"] = mode
