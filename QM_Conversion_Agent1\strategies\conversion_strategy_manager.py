"""
Conversion Strategy Manager for handling multiple conversion approaches.
Manages both AI conversion and SQL toolkit conversion strategies.
"""

from typing import Dict, Any, Optional
from enum import Enum

class ConversionStrategy(Enum):
    """Enumeration of available conversion strategies."""
    AI_CONVERSION = "ai_conversion"
    SQL_TOOLKIT = "sql_toolkit"
    HYBRID = "hybrid"  # Future enhancement

class ConversionStrategyManager:
    """
    Manages different conversion strategies for Oracle to PostgreSQL migration.
    
    This class handles the selection and execution of different conversion approaches:
    1. AI Conversion (current implementation)
    2. SQL Toolkit Conversion (new implementation)
    3. Hybrid approach (future enhancement)
    """
    
    def __init__(self, llm, ai_converter=None, sql_toolkit=None):
        """
        Initialize the strategy manager.
        
        Args:
            llm: Language model instance
            ai_converter: AI conversion instance (existing implementation)
            sql_toolkit: SQL toolkit conversion instance
        """
        self.llm = llm
        self.ai_converter = ai_converter
        self.sql_toolkit = sql_toolkit
        
        # Strategy selection configuration
        self.strategy_config = {
            "default_strategy": ConversionStrategy.AI_CONVERSION,
            "fallback_strategy": ConversionStrategy.SQL_TOOLKIT,
            "enable_fallback": True
        }
    
    def select_conversion_strategy(self, source_context, target_error_context, error_message, previous_feedback=None):
        """
        Select the appropriate conversion strategy based on context and error type.
        
        Args:
            source_context: ErrorContext with Oracle source statements
            target_error_context: ErrorContext with PostgreSQL target statements
            error_message: Deployment error message
            previous_feedback: Previous conversion feedback
            
        Returns:
            ConversionStrategy enum value
        """
        # Strategy selection logic
        
        # Check if this is a target-specific error (no source mapping)
        is_target_specific = (source_context.error_statement_number == 0 or
                             not source_context.error_statement or
                             source_context.error_statement.strip() == "")
        
        # Check for specific error patterns that might benefit from SQL toolkit
        sql_toolkit_indicators = [
            "syntax error",
            "function does not exist",
            "operator does not exist", 
            "type does not exist",
            "column does not exist",
            "relation does not exist"
        ]
        
        error_lower = error_message.lower()
        has_sql_indicators = any(indicator in error_lower for indicator in sql_toolkit_indicators)
        
        # Strategy selection logic
        if previous_feedback and "sql_toolkit" in previous_feedback.lower():
            # If previous feedback suggests using SQL toolkit
            return ConversionStrategy.SQL_TOOLKIT
        elif is_target_specific and has_sql_indicators:
            # Target-specific errors with SQL syntax issues - good for toolkit
            return ConversionStrategy.SQL_TOOLKIT
        else:
            # Default to AI conversion for most cases
            return ConversionStrategy.AI_CONVERSION
    
    def execute_conversion(self, strategy, source_context, target_error_context, target_statements, error_message, previous_feedback=None):
        """
        Execute the conversion using the specified strategy.
        
        Args:
            strategy: ConversionStrategy enum value
            source_context: ErrorContext with Oracle source statements
            target_error_context: ErrorContext with PostgreSQL target statements
            target_statements: List of all target statements for context
            error_message: Deployment error message
            previous_feedback: Previous conversion feedback
            
        Returns:
            Dictionary with conversion results
        """
        try:
            if strategy == ConversionStrategy.AI_CONVERSION:
                return self._execute_ai_conversion(
                    source_context, target_error_context, target_statements, 
                    error_message, previous_feedback
                )
            elif strategy == ConversionStrategy.SQL_TOOLKIT:
                return self._execute_sql_toolkit_conversion(
                    source_context, target_error_context, target_statements,
                    error_message, previous_feedback
                )
            else:
                raise ValueError(f"Unsupported conversion strategy: {strategy}")
                
        except Exception as e:
            print(f"❌ Conversion strategy {strategy.value} failed: {str(e)}")
            
            # Try fallback strategy if enabled
            if self.strategy_config["enable_fallback"] and strategy != self.strategy_config["fallback_strategy"]:
                print(f"🔄 Trying fallback strategy: {self.strategy_config['fallback_strategy'].value}")
                return self.execute_conversion(
                    self.strategy_config["fallback_strategy"],
                    source_context, target_error_context, target_statements,
                    error_message, previous_feedback
                )
            else:
                return None
    
    def _execute_ai_conversion(self, source_context, target_error_context, target_statements, error_message, previous_feedback):
        """Execute AI conversion strategy (existing implementation)."""
        if not self.ai_converter:
            raise ValueError("AI converter not available")
        
        print("🧠 Executing AI conversion strategy...")
        
        # Use the existing AI conversion method
        # This should call the existing convertStatementWithAI method
        return self.ai_converter(source_context, target_error_context, target_statements, error_message, previous_feedback)
    
    def _execute_sql_toolkit_conversion(self, source_context, target_error_context, target_statements, error_message, previous_feedback):
        """Execute SQL toolkit conversion strategy."""
        if not self.sql_toolkit:
            raise ValueError("SQL toolkit not available")
        
        print("🔧 Executing SQL toolkit conversion strategy...")
        
        # Use the SQL toolkit conversion
        return self.sql_toolkit.convert_with_toolkit(
            source_context, target_error_context, target_statements,
            error_message, previous_feedback
        )
    
    def convert_with_strategy_selection(self, source_context, target_error_context, target_statements, error_message, previous_feedback=None):
        """
        Main conversion method that automatically selects and executes the best strategy.
        
        Args:
            source_context: ErrorContext with Oracle source statements
            target_error_context: ErrorContext with PostgreSQL target statements
            target_statements: List of all target statements for context
            error_message: Deployment error message
            previous_feedback: Previous conversion feedback
            
        Returns:
            Dictionary with conversion results including strategy used
        """
        try:
            # Select the best strategy
            selected_strategy = self.select_conversion_strategy(
                source_context, target_error_context, error_message, previous_feedback
            )
            
            print(f"🎯 Selected conversion strategy: {selected_strategy.value}")
            
            # Execute the conversion
            result = self.execute_conversion(
                selected_strategy, source_context, target_error_context,
                target_statements, error_message, previous_feedback
            )
            
            if result:
                # Add strategy information to result
                result["conversion_strategy"] = selected_strategy.value
                result["strategy_selection_reason"] = self._get_strategy_reason(selected_strategy, source_context, error_message)
            
            return result
            
        except Exception as e:
            print(f"❌ Strategy-based conversion failed: {str(e)}")
            return None
    
    def _get_strategy_reason(self, strategy, source_context, error_message):
        """Get human-readable reason for strategy selection."""
        if strategy == ConversionStrategy.AI_CONVERSION:
            return "Selected AI conversion for general Oracle to PostgreSQL migration logic"
        elif strategy == ConversionStrategy.SQL_TOOLKIT:
            is_target_specific = (source_context.error_statement_number == 0 or
                                 not source_context.error_statement or
                                 source_context.error_statement.strip() == "")
            if is_target_specific:
                return "Selected SQL toolkit for target-specific PostgreSQL syntax error"
            else:
                return "Selected SQL toolkit for PostgreSQL-specific syntax conversion"
        else:
            return f"Selected {strategy.value} strategy"
    
    def update_strategy_config(self, config_updates):
        """Update strategy configuration."""
        self.strategy_config.update(config_updates)
        print(f"📊 Updated strategy configuration: {self.strategy_config}")
