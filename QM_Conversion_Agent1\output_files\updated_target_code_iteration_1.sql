set search_path to LAB;
create or replace function LAB.FN_GETPATIENTDETAILS(IN_UHID IN VARCHAR)
returns varchar
language plpgsql
security definer
as $BODY$
declare
V_PATIENTDETAILS VARCHAR(500);
begin
set search_path to LAB;
SELECT (SELECT TLM.TITLETYPE
        FROM EHIS.TITLEMASTER TLM
        WHERE P.TITLE = TLM.TITLECODE) || ' ' || P.FIRSTNAME || ' ' || P.MIDDLENAME || ' ' || P.LASTNAME || ' ' ||
       (SELECT SM.SUFFIXNAME
        FROM EHIS.SUFFIXMASTER SM
        WHERE P.SUFIX = SM.SUFFIXCODE) || ' /' || LD.LOVDETAILVALUE || ' /' ||
       FLOOR(EXTRACT(YEAR FROM AGE(P.BIRTHDATE)) ) || 'Yr' || ' ' ||
       EXTRACT(MONTH FROM AGE(P.BIRTHDATE)) || 'Mth' || ' ' ||
       EXTRACT(DAY FROM AGE(P.BIRTHDATE)) || 'Days'
INTO V_PATIENTDETAILS
FROM REGISTRATION.PATIENT P
JOIN EHIS.LOVDETAIL LD ON LD.LOVDETAILID = P.GENDER
WHERE P.EMERGENCYNO = IN_UHID OR P.PREREGISTRATIONNO = IN_UHID OR P.UHID = IN_UHID;
RETURN V_PATIENTDETAILS;
end;$BODY$;