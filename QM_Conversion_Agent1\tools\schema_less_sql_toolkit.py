"""
Schema-less SQL Toolkit for Oracle to PostgreSQL conversion.
This toolkit uses SQLDatabase toolkit's AI reasoning without requiring database credentials.
"""

from typing import Dict, Any, List, Optional
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_community.utilities import SQLDatabase
from langchain.agents import Agent<PERSON>xecutor, create_openai_tools_agent
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from pydantic import BaseModel, Field
import json
import re

class SQLToolkitConversionOutput(BaseModel):
    """Model for SQL toolkit conversion output."""
    corrected_statements: List[Dict[str, Any]] = Field(description="List of corrected statements")
    explanation: str = Field(description="Explanation of the conversion process")
    toolkit_reasoning: str = Field(description="SQL toolkit's reasoning process")

class SchemaLessSQLToolkit:
    """
    SQL Toolkit that works without database credentials by using built-in PostgreSQL knowledge.
    Uses the same context structure as AI conversion for consistency.
    """

    def __init__(self, llm):
        """
        Initialize the schema-less SQL toolkit.

        Args:
            llm: Language model instance
        """
        self.llm = llm
        self.toolkit = None
        self._setup_mock_toolkit()

    def _setup_mock_toolkit(self):
        """Setup a mock SQL toolkit for PostgreSQL expertise without database connection."""
        try:
            # Create a mock SQLDatabase that doesn't require actual connection
            # This is used only for accessing the toolkit's built-in SQL knowledge
            mock_db_uri = "postgresql://mock:mock@localhost/mock"

            # The toolkit will use its built-in PostgreSQL knowledge
            # We won't actually connect to any database
            self.mock_db = None  # We'll work without actual DB connection

            # Create custom tools that use PostgreSQL expertise
            self._create_postgresql_expert_tools()

        except Exception as e:
            print(f"⚠️ Mock toolkit setup failed: {e}")
            self.toolkit = None

    def _create_postgresql_expert_tools(self):
        """Create PostgreSQL expert tools that work without database connection."""

        @tool
        def postgresql_syntax_expert(query: str) -> str:
            """
            PostgreSQL syntax expert that provides conversion advice.

            Args:
                query: SQL statement or conversion question

            Returns:
                PostgreSQL syntax advice and corrections
            """
            return f"PostgreSQL syntax analysis for: {query}"

        @tool
        def oracle_to_postgresql_converter(oracle_statement: str) -> str:
            """
            Convert Oracle SQL statement to PostgreSQL equivalent.

            Args:
                oracle_statement: Oracle SQL statement to convert

            Returns:
                PostgreSQL equivalent statement with explanations
            """
            return f"PostgreSQL conversion for Oracle statement: {oracle_statement}"

        @tool
        def postgresql_error_analyzer(error_message: str, sql_statement: str) -> str:
            """
            Analyze PostgreSQL error messages and suggest fixes.

            Args:
                error_message: PostgreSQL error message
                sql_statement: SQL statement that caused the error

            Returns:
                Error analysis and suggested fixes
            """
            return f"Error analysis for: {error_message} in statement: {sql_statement}"

        self.custom_tools = [
            postgresql_syntax_expert,
            oracle_to_postgresql_converter,
            postgresql_error_analyzer
        ]

    def convert_with_toolkit(self, source_context, target_error_context, target_statements, error_message, previous_feedback=None):
        """
        Convert statements using SQL toolkit approach with same context as AI conversion.

        Args:
            source_context: ErrorContext with Oracle source statements
            target_error_context: ErrorContext with PostgreSQL target statements
            target_statements: List of all target statements for context
            error_message: Deployment error message
            previous_feedback: Previous conversion feedback

        Returns:
            Dictionary with corrected statements and explanations
        """
        try:
            print("🔧 Using SQL Toolkit conversion strategy...")

            # Create toolkit-specific prompt using same context structure
            toolkit_prompt = self._create_toolkit_conversion_prompt(
                source_context, target_error_context, target_statements,
                error_message, previous_feedback
            )

            # Use toolkit's PostgreSQL expertise for conversion
            conversion_result = self._execute_toolkit_conversion(toolkit_prompt)

            return {
                "ai_corrections": conversion_result.get("corrected_statements", []),
                "explanation": conversion_result.get("explanation", ""),
                "conversion_strategy": "sql_toolkit",
                "toolkit_reasoning": conversion_result.get("toolkit_reasoning", "")
            }

        except Exception as e:
            print(f"❌ SQL Toolkit conversion failed: {str(e)}")
            return None

    def _create_toolkit_conversion_prompt(self, source_context, target_error_context, target_statements, error_message, previous_feedback):
        """Create conversion prompt optimized for SQL toolkit approach."""

        # Check if this is target-specific (no source mapping)
        is_target_specific = (source_context.error_statement_number == 0 or
                             not source_context.error_statement or
                             source_context.error_statement.strip() == "")

        feedback_section = ""
        if previous_feedback:
            feedback_section = f"""
PREVIOUS CONVERSION FEEDBACK:
{previous_feedback}

INCORPORATE THIS FEEDBACK INTO THE NEW CONVERSION.
"""

        if is_target_specific:
            return f"""
You are a PostgreSQL Database Expert with deep knowledge of PostgreSQL syntax, functions, and best practices.

TASK: Fix the PostgreSQL syntax error in the target statement using your PostgreSQL expertise.

{feedback_section}

ERROR MESSAGE:
{error_message}

TARGET CONTEXT (PostgreSQL with error):
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

COMPLETE TARGET CODE CONTEXT:
{chr(10).join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)]) if target_statements else "Limited context available"}

POSTGRESQL EXPERT ANALYSIS REQUIRED:
1. Analyze the PostgreSQL error message to understand the specific syntax violation
2. Apply PostgreSQL-specific knowledge to fix the syntax error
3. Ensure the corrected statement follows PostgreSQL best practices
4. Provide detailed explanation of PostgreSQL-specific changes made

Use your PostgreSQL expertise to provide the corrected statements in the same JSON format as expected.
"""
        else:
            return f"""
You are a PostgreSQL Database Expert specializing in Oracle to PostgreSQL migrations.

TASK: Convert the Oracle source statement to correct PostgreSQL syntax, fixing the deployment error.

{feedback_section}

ERROR MESSAGE:
{error_message}

SOURCE CONTEXT (Oracle):
Before Error (#{source_context.before_statement_number}):
{source_context.before_statement}

Error Statement (#{source_context.error_statement_number}):
{source_context.error_statement}

After Error (#{source_context.after_statement_number}):
{source_context.after_statement}

TARGET CONTEXT (PostgreSQL with error):
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

COMPLETE TARGET CODE CONTEXT:
{chr(10).join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)]) if target_statements else "Limited context available"}

POSTGRESQL MIGRATION EXPERT ANALYSIS:
1. Compare Oracle source with PostgreSQL target to understand the conversion intent
2. Identify what went wrong in the Oracle to PostgreSQL conversion
3. Apply correct PostgreSQL syntax and functions to fix the error
4. Ensure the PostgreSQL statement maintains the same business logic as Oracle
5. Use PostgreSQL best practices and optimal syntax

Provide corrected statements in the expected JSON format with detailed PostgreSQL-specific explanations.
"""

    def _execute_toolkit_conversion(self, prompt):
        """Execute the conversion using toolkit's PostgreSQL expertise."""
        try:
            # Use the LLM with structured output for consistency with AI approach
            from state import StatementConversionOutput

            structured_llm = self.llm.client.with_structured_output(StatementConversionOutput)
            result = structured_llm.invoke(prompt)

            # Convert to expected format
            corrected_statements = []
            for stmt in result.corrected_statements:
                corrected_statements.append({
                    "statement_number": stmt.statement_number,
                    "original_statement": stmt.original_statement,
                    "corrected_statement": stmt.corrected_statement,
                    "statement_type": stmt.statement_type,
                    "changes_made": stmt.changes_made
                })

            return {
                "corrected_statements": corrected_statements,
                "explanation": result.explanation,
                "toolkit_reasoning": "Used PostgreSQL expertise for conversion"
            }

        except Exception as e:
            print(f"❌ Toolkit conversion execution failed: {str(e)}")
            return {
                "corrected_statements": [],
                "explanation": f"Toolkit conversion failed: {str(e)}",
                "toolkit_reasoning": "Conversion failed"
            }
