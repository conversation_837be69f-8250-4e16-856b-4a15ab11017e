"""
SQL Toolkit Agent for Oracle to PostgreSQL conversion.
Creates an agent using langchain SQLDatabaseToolkit for statement conversion.
"""

from state import StatementConversionOutput

def create_sql_conversion_agent(llm, source_context, target_error_context, target_statements, error_message, previous_feedback=None):
    """
    Create SQL conversion using your existing LLM with PostgreSQL expertise.
    Uses your context structure for Oracle to PostgreSQL conversion.
    """
    try:
        print("🔧 Creating SQL conversion agent...")

        # Create agent input using your context
        agent_input = _create_agent_input_from_context(
            source_context, target_error_context, target_statements,
            error_message, previous_feedback
        )

        # Use structured output for reliable parsing (same as convertStatementWithAI)
        print("🤖 Using your LLM with PostgreSQL expertise and structured output...")
        structured_llm = llm.client.with_structured_output(StatementConversionOutput)
        ai_result = structured_llm.invoke(agent_input)

        corrected_stmts = ai_result.corrected_statements
        explanation = ai_result.explanation

        print(f"🎯 SQL Toolkit provided {len(corrected_stmts)} corrected statements")
        print(f"📝 Explanation: {explanation}")

        if not corrected_stmts:
            print("⚠️ No corrected statements returned")
            return None

        # Convert to expected format (same as convertStatementWithAI)
        ai_corrections = []
        for stmt in corrected_stmts:
            ai_corrections.append({
                "statement_number": stmt.statement_number,
                "original_statement": stmt.original_statement,
                "corrected_statement": stmt.corrected_statement,
                "statement_type": stmt.statement_type,
                "changes_made": stmt.changes_made
            })

        return {
            "ai_corrections": ai_corrections,
            "explanation": explanation,
            "conversion_strategy": "sql_toolkit",
            "original_target_statements": target_statements
        }

    except Exception as e:
        print(f"❌ SQL agent creation failed: {str(e)}")
        return None


def _create_agent_input_from_context(source_context, target_error_context, target_statements, error_message, previous_feedback):
    """Create agent input using your existing context structure."""

    # Check if target-specific (no source mapping)
    is_target_specific = (source_context.error_statement_number == 0 or
                         not source_context.error_statement or
                         source_context.error_statement.strip() == "")

    feedback_section = ""
    if previous_feedback:
        feedback_section = f"\nPREVIOUS FEEDBACK: {previous_feedback}\nIncorporate this feedback."

    if is_target_specific:
        return f"""You are a PostgreSQL expert specializing in Oracle to PostgreSQL migrations.

TASK: Fix the PostgreSQL syntax error in the target statement using your PostgreSQL expertise.

ERROR MESSAGE: {error_message}

TARGET STATEMENT WITH ERROR:
Statement #{target_error_context.error_statement_number}: {target_error_context.error_statement}

CONTEXT:
Before: {target_error_context.before_statement}
After: {target_error_context.after_statement}

{feedback_section}

COMPLETE TARGET CODE CONTEXT:
{chr(10).join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)]) if target_statements else "Limited context available"}

POSTGRESQL EXPERT ANALYSIS REQUIRED:
1. Analyze the PostgreSQL error message to understand the specific syntax violation
2. Apply PostgreSQL-specific knowledge to fix the syntax error
3. Ensure the corrected statement follows PostgreSQL best practices
4. Provide detailed explanation of PostgreSQL-specific changes made

CRITICAL: You must return a correction for statement number {target_error_context.error_statement_number} (the error statement).
The statement_number in your response must be exactly {target_error_context.error_statement_number}.

Use your PostgreSQL expertise to provide the corrected statements."""
    else:
        return f"""You are a PostgreSQL expert specializing in Oracle to PostgreSQL migrations.

TASK: Convert the Oracle source statement to correct PostgreSQL syntax, fixing the deployment error.

ERROR MESSAGE: {error_message}

ORACLE SOURCE: {source_context.error_statement}
POSTGRESQL TARGET (with error): {target_error_context.error_statement}

CONTEXT:
Oracle Before: {source_context.before_statement}
Oracle After: {source_context.after_statement}
PostgreSQL Before: {target_error_context.before_statement}
PostgreSQL After: {target_error_context.after_statement}

{feedback_section}

COMPLETE TARGET CODE CONTEXT:
{chr(10).join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)]) if target_statements else "Limited context available"}

POSTGRESQL MIGRATION EXPERT ANALYSIS:
1. Compare Oracle source with PostgreSQL target to understand the conversion intent
2. Identify what went wrong in the Oracle to PostgreSQL conversion
3. Apply correct PostgreSQL syntax and functions to fix the error
4. Ensure the PostgreSQL statement maintains the same business logic as Oracle
5. Use PostgreSQL best practices and optimal syntax

CRITICAL: You must return a correction for statement number {target_error_context.error_statement_number} (the error statement).
The statement_number in your response must be exactly {target_error_context.error_statement_number}.

Provide corrected statements with detailed PostgreSQL-specific explanations."""



