"""
SQL Toolkit Agent for Oracle to PostgreSQL conversion.
Creates an agent using langchain SQLDatabaseToolkit for statement conversion.
"""

import json
import re

def create_sql_conversion_agent(llm, source_context, target_error_context, target_statements, error_message, previous_feedback=None):
    """
    Create SQL conversion using your existing LLM with PostgreSQL expertise.
    Uses your context structure for Oracle to PostgreSQL conversion.
    """
    try:
        print("🔧 Creating SQL conversion agent...")

        # Create agent input using your context
        agent_input = _create_agent_input_from_context(
            source_context, target_error_context, target_statements,
            error_message, previous_feedback
        )

        # Use your existing LLM directly with PostgreSQL expertise
        print("🤖 Using your LLM with PostgreSQL expertise...")
        response = llm.invoke(agent_input)
        return _parse_direct_llm_result(response, target_error_context)

    except Exception as e:
        print(f"❌ SQL agent creation failed: {str(e)}")
        return None


def _parse_direct_llm_result(response, target_error_context):
    """Parse direct LLM response into expected format."""
    try:
        import json
        import re

        # Get response content
        if hasattr(response, 'content'):
            output = response.content
        else:
            output = str(response)

        # Extract JSON from output
        json_match = re.search(r'\{[^}]*"corrected_statement"[^}]*\}', output, re.DOTALL)

        if json_match:
            try:
                parsed = json.loads(json_match.group())
                corrected_statement = parsed.get("corrected_statement", target_error_context.error_statement)
                explanation = parsed.get("explanation", "SQL toolkit conversion")
                changes_made = parsed.get("changes_made", "Applied PostgreSQL expertise")

                return {
                    "ai_corrections": [{
                        "statement_number": target_error_context.error_statement_number,
                        "original_statement": target_error_context.error_statement,
                        "corrected_statement": corrected_statement,
                        "statement_type": "error_statement",
                        "changes_made": changes_made
                    }],
                    "explanation": explanation,
                    "conversion_strategy": "sql_toolkit",
                    "original_target_statements": []
                }
            except json.JSONDecodeError:
                pass

        # Fallback if JSON parsing fails
        return {
            "ai_corrections": [{
                "statement_number": target_error_context.error_statement_number,
                "original_statement": target_error_context.error_statement,
                "corrected_statement": target_error_context.error_statement,
                "statement_type": "error_statement",
                "changes_made": "Direct LLM processing completed"
            }],
            "explanation": f"Direct LLM output: {output}",
            "conversion_strategy": "sql_toolkit",
            "original_target_statements": []
        }

    except Exception as e:
        print(f"❌ Direct LLM result parsing failed: {str(e)}")
        return None


def _create_agent_input_from_context(source_context, target_error_context, target_statements, error_message, previous_feedback):
    """Create agent input using your existing context structure."""

    # Check if target-specific (no source mapping)
    is_target_specific = (source_context.error_statement_number == 0 or
                         not source_context.error_statement or
                         source_context.error_statement.strip() == "")

    feedback_section = ""
    if previous_feedback:
        feedback_section = f"\nPREVIOUS FEEDBACK: {previous_feedback}\nIncorporate this feedback."

    if is_target_specific:
        return f"""You are a PostgreSQL expert specializing in Oracle to PostgreSQL migrations.

TASK: Fix the PostgreSQL syntax error in the target statement using your PostgreSQL expertise.

ERROR MESSAGE: {error_message}

TARGET STATEMENT WITH ERROR:
Statement #{target_error_context.error_statement_number}: {target_error_context.error_statement}

CONTEXT:
Before: {target_error_context.before_statement}
After: {target_error_context.after_statement}

{feedback_section}

IMPORTANT: Return your response in this EXACT JSON format:
{{
  "corrected_statement": "the fixed PostgreSQL statement",
  "explanation": "detailed explanation of the fix",
  "changes_made": "specific changes made"
}}

Use your PostgreSQL expertise to analyze and fix the statement."""
    else:
        return f"""You are a PostgreSQL expert specializing in Oracle to PostgreSQL migrations.

TASK: Convert the Oracle source statement to correct PostgreSQL syntax, fixing the deployment error.

ERROR MESSAGE: {error_message}

ORACLE SOURCE: {source_context.error_statement}
POSTGRESQL TARGET (with error): {target_error_context.error_statement}

CONTEXT:
Oracle Before: {source_context.before_statement}
Oracle After: {source_context.after_statement}
PostgreSQL Before: {target_error_context.before_statement}
PostgreSQL After: {target_error_context.after_statement}

{feedback_section}

IMPORTANT: Return your response in this EXACT JSON format:
{{
  "corrected_statement": "the fixed PostgreSQL statement",
  "explanation": "detailed explanation of the conversion and fix",
  "changes_made": "specific changes made for PostgreSQL compatibility"
}}

Use your PostgreSQL expertise to convert and fix the statement."""



