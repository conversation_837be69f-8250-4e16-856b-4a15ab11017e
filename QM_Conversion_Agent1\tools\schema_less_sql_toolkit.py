"""
SQL Toolkit Agent for Oracle to PostgreSQL conversion.
Creates an agent using langchain SQLDatabaseToolkit for statement conversion.
"""

from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_community.utilities import SQLDatabase
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder

def create_sql_conversion_agent(llm, source_context, target_error_context, target_statements, error_message, previous_feedback=None):
    """
    Create SQL conversion agent using langchain SQLDatabaseToolkit.
    Uses your context structure for Oracle to PostgreSQL conversion.
    """
    try:
        print("🔧 Creating SQL conversion agent...")

        # Try to create SQLDatabase toolkit (without actual connection)
        try:
            mock_db = SQLDatabase.from_uri("postgresql://mock:mock@localhost/mock_db")
            toolkit = SQLDatabaseToolkit(db=mock_db, llm=llm)
            sql_tools = toolkit.get_tools()
            print(f"✅ SQL Toolkit created with {len(sql_tools)} tools")
        except Exception as e:
            print(f"⚠️ SQLDatabase creation failed: {e}")
            return None

        # Create agent input using your context
        agent_input = _create_agent_input_from_context(
            source_context, target_error_context, target_statements,
            error_message, previous_feedback
        )

        # Create SQL conversion prompt
        sql_prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a PostgreSQL expert specializing in Oracle to PostgreSQL migrations.

You have access to SQL database tools. Use them to analyze and convert SQL statements.

IMPORTANT: Return your response in this EXACT JSON format:
{
  "corrected_statement": "the fixed PostgreSQL statement",
  "explanation": "detailed explanation of the fix",
  "changes_made": "specific changes made"
}

Use the SQL tools to understand PostgreSQL syntax and provide accurate conversions."""),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ])

        # Create agent
        agent = create_openai_tools_agent(
            llm=llm,
            tools=sql_tools,
            prompt=sql_prompt
        )

        # Create agent executor
        agent_executor = AgentExecutor(
            agent=agent,
            tools=sql_tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=3
        )

        print("🤖 Executing SQL agent...")
        result = agent_executor.invoke({"input": agent_input})

        # Parse result and return in expected format
        return _parse_agent_result(result, target_error_context)

    except Exception as e:
        print(f"❌ SQL agent creation failed: {str(e)}")
        return None


def _create_agent_input_from_context(source_context, target_error_context, target_statements, error_message, previous_feedback):
    """Create agent input using your existing context structure."""

    # Check if target-specific (no source mapping)
    is_target_specific = (source_context.error_statement_number == 0 or
                         not source_context.error_statement or
                         source_context.error_statement.strip() == "")

    feedback_section = ""
    if previous_feedback:
        feedback_section = f"\nPREVIOUS FEEDBACK: {previous_feedback}\nIncorporate this feedback."

    if is_target_specific:
        return f"""Fix this PostgreSQL syntax error using SQL tools:

ERROR MESSAGE: {error_message}

TARGET STATEMENT WITH ERROR:
Statement #{target_error_context.error_statement_number}: {target_error_context.error_statement}

CONTEXT:
Before: {target_error_context.before_statement}
After: {target_error_context.after_statement}

{feedback_section}

Use SQL tools to analyze and fix the PostgreSQL statement. Return JSON format."""
    else:
        return f"""Convert Oracle to PostgreSQL and fix error using SQL tools:

ERROR MESSAGE: {error_message}

ORACLE SOURCE: {source_context.error_statement}
POSTGRESQL TARGET (with error): {target_error_context.error_statement}

CONTEXT:
Oracle Before: {source_context.before_statement}
Oracle After: {source_context.after_statement}
PostgreSQL Before: {target_error_context.before_statement}
PostgreSQL After: {target_error_context.after_statement}

{feedback_section}

Use SQL tools to convert and fix. Return JSON format."""


def _parse_agent_result(agent_result, target_error_context):
    """Parse agent result into expected format."""
    try:
        import json
        import re

        output = agent_result.get("output", "")

        # Extract JSON from output
        json_match = re.search(r'\{[^}]*"corrected_statement"[^}]*\}', output, re.DOTALL)

        if json_match:
            try:
                parsed = json.loads(json_match.group())
                corrected_statement = parsed.get("corrected_statement", target_error_context.error_statement)
                explanation = parsed.get("explanation", "SQL agent conversion")
                changes_made = parsed.get("changes_made", "Applied SQL toolkit conversion")

                return {
                    "ai_corrections": [{
                        "statement_number": target_error_context.error_statement_number,
                        "original_statement": target_error_context.error_statement,
                        "corrected_statement": corrected_statement,
                        "statement_type": "error_statement",
                        "changes_made": changes_made
                    }],
                    "explanation": explanation,
                    "conversion_strategy": "sql_toolkit",
                    "original_target_statements": []
                }
            except json.JSONDecodeError:
                pass

        # Fallback if JSON parsing fails
        return {
            "ai_corrections": [{
                "statement_number": target_error_context.error_statement_number,
                "original_statement": target_error_context.error_statement,
                "corrected_statement": target_error_context.error_statement,
                "statement_type": "error_statement",
                "changes_made": "SQL agent processing completed"
            }],
            "explanation": f"SQL agent output: {output}",
            "conversion_strategy": "sql_toolkit",
            "original_target_statements": []
        }

    except Exception as e:
        print(f"❌ Result parsing failed: {str(e)}")
        return None
