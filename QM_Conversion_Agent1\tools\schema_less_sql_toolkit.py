"""
SQL Toolkit Agent for Oracle to PostgreSQL conversion.
Creates an agent using langchain SQLDatabaseToolkit for statement conversion.
"""

from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_community.utilities import SQLDatabase
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_core.prompts import Chat<PERSON>rompt<PERSON>emplate, MessagesPlaceholder
from state import StatementConversionOutput

def create_sql_conversion_agent(llm, source_context, target_error_context, target_statements, error_message, previous_feedback=None):
    """
    Create SQL conversion using your existing LLM with PostgreSQL expertise.
    Uses your context structure for Oracle to PostgreSQL conversion.
    """
    try:
        print("🔧 Creating SQL conversion agent with SQLDatabaseToolkit...")

        # Try to create SQLDatabaseToolkit with your database
        try:
            # Use your actual database connection
            db_uri = "postgresql://qmig:Qu%40drant%40754@172.16.6.5/airflow_test"
            db = SQLDatabase.from_uri(db_uri)

            # Create SQLDatabaseToolkit with your LLM
            toolkit = SQLDatabaseToolkit(db=db, llm=llm)
            sql_tools = toolkit.get_tools()
            print(f"✅ SQLDatabaseToolkit created with {len(sql_tools)} tools")

        except Exception as e:
            print(f"⚠️ SQLDatabaseToolkit creation failed: {e}")
            print("🔄 Falling back to structured output approach...")
            return _fallback_to_structured_output(llm, source_context, target_error_context, target_statements, error_message, previous_feedback)

        # Create agent input using your context
        agent_input = _create_agent_input_from_context(
            source_context, target_error_context, target_statements,
            error_message, previous_feedback
        )

        # Create SQL conversion prompt for agent
        sql_prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a PostgreSQL expert specializing in Oracle to PostgreSQL migrations.

You have access to SQL database tools. Use them to analyze and convert SQL statements.

IMPORTANT: You must return a correction for the specific statement number mentioned in the user's request.
Use the SQL tools to understand PostgreSQL syntax and provide accurate conversions.

Return your response with:
- The corrected SQL statement
- Detailed explanation of the fix
- Specific changes made"""),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ])

        # Create agent with SQL tools
        agent = create_openai_tools_agent(
            llm=llm,
            tools=sql_tools,
            prompt=sql_prompt
        )

        # Create agent executor
        agent_executor = AgentExecutor(
            agent=agent,
            tools=sql_tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=5
        )

        print("🤖 Executing SQL agent with database tools...")
        result = agent_executor.invoke({"input": agent_input})

        # Parse agent result and convert to your expected format
        return _parse_agent_result(result, target_error_context, target_statements)

    except Exception as e:
        print(f"❌ SQL agent creation failed: {str(e)}")
        print("🔄 Falling back to structured output approach...")
        return _fallback_to_structured_output(llm, source_context, target_error_context, target_statements, error_message, previous_feedback)


def _create_agent_input_from_context(source_context, target_error_context, target_statements, error_message, previous_feedback):
    """Create agent input using your existing context structure."""

    # Check if target-specific (no source mapping)
    is_target_specific = (source_context.error_statement_number == 0 or
                         not source_context.error_statement or
                         source_context.error_statement.strip() == "")

    feedback_section = ""
    if previous_feedback:
        feedback_section = f"\nPREVIOUS FEEDBACK: {previous_feedback}\nIncorporate this feedback."

    if is_target_specific:
        return f"""You are a PostgreSQL expert specializing in Oracle to PostgreSQL migrations.

TASK: Fix the PostgreSQL syntax error in the target statement using your PostgreSQL expertise.

ERROR MESSAGE: {error_message}

TARGET STATEMENT WITH ERROR:
Statement #{target_error_context.error_statement_number}: {target_error_context.error_statement}

CONTEXT:
Before: {target_error_context.before_statement}
After: {target_error_context.after_statement}

{feedback_section}

COMPLETE TARGET CODE CONTEXT:
{chr(10).join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)]) if target_statements else "Limited context available"}

POSTGRESQL EXPERT ANALYSIS REQUIRED:
1. Analyze the PostgreSQL error message to understand the specific syntax violation
2. Apply PostgreSQL-specific knowledge to fix the syntax error
3. Ensure the corrected statement follows PostgreSQL best practices
4. Provide detailed explanation of PostgreSQL-specific changes made

CRITICAL: You must return a correction for statement number {target_error_context.error_statement_number} (the error statement).
The statement_number in your response must be exactly {target_error_context.error_statement_number}.

Use your PostgreSQL expertise to provide the corrected statements."""
    else:
        return f"""You are a PostgreSQL expert specializing in Oracle to PostgreSQL migrations.

TASK: Convert the Oracle source statement to correct PostgreSQL syntax, fixing the deployment error.

ERROR MESSAGE: {error_message}

ORACLE SOURCE: {source_context.error_statement}
POSTGRESQL TARGET (with error): {target_error_context.error_statement}

CONTEXT:
Oracle Before: {source_context.before_statement}
Oracle After: {source_context.after_statement}
PostgreSQL Before: {target_error_context.before_statement}
PostgreSQL After: {target_error_context.after_statement}

{feedback_section}

COMPLETE TARGET CODE CONTEXT:
{chr(10).join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)]) if target_statements else "Limited context available"}

POSTGRESQL MIGRATION EXPERT ANALYSIS:
1. Compare Oracle source with PostgreSQL target to understand the conversion intent
2. Identify what went wrong in the Oracle to PostgreSQL conversion
3. Apply correct PostgreSQL syntax and functions to fix the error
4. Ensure the PostgreSQL statement maintains the same business logic as Oracle
5. Use PostgreSQL best practices and optimal syntax

CRITICAL: You must return a correction for statement number {target_error_context.error_statement_number} (the error statement).
The statement_number in your response must be exactly {target_error_context.error_statement_number}.

Provide corrected statements with detailed PostgreSQL-specific explanations."""


def _fallback_to_structured_output(llm, source_context, target_error_context, target_statements, error_message, previous_feedback):
    """Fallback to structured output approach when SQLDatabaseToolkit fails."""
    try:
        print("🔄 Using structured output fallback...")

        # Create agent input using your context
        agent_input = _create_agent_input_from_context(
            source_context, target_error_context, target_statements,
            error_message, previous_feedback
        )

        # Use structured output for reliable parsing (same as convertStatementWithAI)
        structured_llm = llm.client.with_structured_output(StatementConversionOutput)
        ai_result = structured_llm.invoke(agent_input)

        corrected_stmts = ai_result.corrected_statements
        explanation = ai_result.explanation

        print(f"🎯 Structured output provided {len(corrected_stmts)} corrected statements")
        print(f"📝 Explanation: {explanation}")

        if not corrected_stmts:
            print("⚠️ No corrected statements returned")
            return None

        # Convert to expected format (same as convertStatementWithAI)
        ai_corrections = []
        for stmt in corrected_stmts:
            # Ensure the statement number matches the error statement number
            if stmt.statement_number != target_error_context.error_statement_number:
                print(f"⚠️ Returned statement #{stmt.statement_number}, correcting to #{target_error_context.error_statement_number}")
                stmt.statement_number = target_error_context.error_statement_number

            ai_corrections.append({
                "statement_number": stmt.statement_number,
                "original_statement": stmt.original_statement,
                "corrected_statement": stmt.corrected_statement,
                "statement_type": stmt.statement_type,
                "changes_made": stmt.changes_made
            })

        return {
            "ai_corrections": ai_corrections,
            "explanation": explanation,
            "conversion_strategy": "sql_toolkit",
            "original_target_statements": target_statements
        }

    except Exception as e:
        print(f"❌ Structured output fallback failed: {str(e)}")
        return None


def _parse_agent_result(result, target_error_context, target_statements):
    """Parse agent result and convert to your expected format."""
    try:
        import re

        output = result.get("output", "")
        print(f"🔍 Agent output: {output}")

        # Extract corrected statement from agent output
        # Look for SQL statement patterns
        sql_patterns = [
            r"```sql\n(.*?)\n```",
            r"```\n(.*?)\n```",
            r"corrected.*?statement.*?:\s*(.*?)(?:\n|$)",
            r"fixed.*?statement.*?:\s*(.*?)(?:\n|$)"
        ]

        corrected_statement = target_error_context.error_statement  # Default fallback

        for pattern in sql_patterns:
            match = re.search(pattern, output, re.DOTALL | re.IGNORECASE)
            if match:
                corrected_statement = match.group(1).strip()
                break

        # Extract explanation
        explanation_patterns = [
            r"explanation.*?:\s*(.*?)(?:\n\n|$)",
            r"fix.*?:\s*(.*?)(?:\n\n|$)",
            r"changes.*?:\s*(.*?)(?:\n\n|$)"
        ]

        explanation = "SQL agent conversion"
        for pattern in explanation_patterns:
            match = re.search(pattern, output, re.DOTALL | re.IGNORECASE)
            if match:
                explanation = match.group(1).strip()
                break

        # Create the expected format
        ai_corrections = [{
            "statement_number": target_error_context.error_statement_number,
            "original_statement": target_error_context.error_statement,
            "corrected_statement": corrected_statement,
            "statement_type": "error_statement",
            "changes_made": f"Applied SQLDatabaseToolkit analysis: {explanation[:100]}..."
        }]

        return {
            "ai_corrections": ai_corrections,
            "explanation": explanation,
            "conversion_strategy": "sql_toolkit",
            "original_target_statements": target_statements
        }

    except Exception as e:
        print(f"❌ Agent result parsing failed: {str(e)}")
        return None

