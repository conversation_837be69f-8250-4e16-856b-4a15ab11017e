"""
Schema-less SQL Toolkit for Oracle to PostgreSQL conversion.
This toolkit uses SQLDatabase toolkit's AI reasoning without requiring database credentials.
"""

from typing import Dict, Any, List, Optional
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_community.utilities import SQLDatabase
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from pydantic import BaseModel, Field
import json
import re

class SQLToolkitConversionOutput(BaseModel):
    """Model for SQL toolkit conversion output."""
    corrected_statements: List[Dict[str, Any]] = Field(description="List of corrected statements")
    explanation: str = Field(description="Explanation of the conversion process")
    toolkit_reasoning: str = Field(description="SQL toolkit's reasoning process")

class SchemaLessSQLToolkit:
    """
    SQL Toolkit that works without database credentials by using built-in PostgreSQL knowledge.
    Uses the same context structure as AI conversion for consistency.
    """

    def __init__(self, llm):
        """
        Initialize the schema-less SQL toolkit.

        Args:
            llm: Language model instance
        """
        self.llm = llm
        self.toolkit = None
        self.agent_executor = None
        self._setup_mock_toolkit()

    def _setup_mock_toolkit(self):
        """Setup SQL toolkit for PostgreSQL expertise without requiring actual database connection."""
        try:
            # Create a mock SQLDatabase for toolkit initialization
            # We'll use the toolkit's tools but won't execute actual database queries
            mock_db_uri = "postgresql://mock:mock@localhost/mock_db"

            # Create a mock database object that won't actually connect
            # This allows us to initialize the toolkit and access its tools
            try:
                # Try to create SQLDatabase object (won't actually connect)
                self.mock_db = SQLDatabase.from_uri(mock_db_uri)

                # Initialize the actual SQLDatabaseToolkit with mock database
                self.toolkit = SQLDatabaseToolkit(db=self.mock_db, llm=self.llm)

                # Get the toolkit's built-in tools
                self.sql_tools = self.toolkit.get_tools()
                print(f"🔧 SQL Toolkit initialized with {len(self.sql_tools)} tools")

                # Create agent executor with SQL toolkit
                self._setup_sql_agent()

            except Exception as db_error:
                print(f"⚠️ Could not create actual SQLDatabase, using custom tools: {db_error}")
                # Fallback to custom tools if SQLDatabase creation fails
                self.toolkit = None
                self.sql_tools = []
                self._create_postgresql_expert_tools()

        except Exception as e:
            print(f"⚠️ Toolkit setup failed: {e}")
            self.toolkit = None
            self.sql_tools = []

    def _create_postgresql_expert_tools(self):
        """Create PostgreSQL expert tools that work without database connection."""

        @tool
        def postgresql_syntax_expert(query: str) -> str:
            """
            PostgreSQL syntax expert that provides conversion advice.

            Args:
                query: SQL statement or conversion question

            Returns:
                PostgreSQL syntax advice and corrections
            """
            return f"PostgreSQL syntax analysis for: {query}"

        @tool
        def oracle_to_postgresql_converter(oracle_statement: str) -> str:
            """
            Convert Oracle SQL statement to PostgreSQL equivalent.

            Args:
                oracle_statement: Oracle SQL statement to convert

            Returns:
                PostgreSQL equivalent statement with explanations
            """
            return f"PostgreSQL conversion for Oracle statement: {oracle_statement}"

        @tool
        def postgresql_error_analyzer(error_message: str, sql_statement: str) -> str:
            """
            Analyze PostgreSQL error messages and suggest fixes.

            Args:
                error_message: PostgreSQL error message
                sql_statement: SQL statement that caused the error

            Returns:
                Error analysis and suggested fixes
            """
            return f"Error analysis for: {error_message} in statement: {sql_statement}"

        self.custom_tools = [
            postgresql_syntax_expert,
            oracle_to_postgresql_converter,
            postgresql_error_analyzer
        ]

    def _setup_sql_agent(self):
        """Setup SQL agent executor using the toolkit."""
        try:
            if self.toolkit and self.sql_tools:
                # Create a prompt template for SQL conversion
                sql_conversion_prompt = ChatPromptTemplate.from_messages([
                    ("system", """You are a PostgreSQL expert specializing in Oracle to PostgreSQL migrations.

You have access to SQL database tools that can help you understand PostgreSQL syntax and best practices.
Use these tools to analyze and convert SQL statements from Oracle to PostgreSQL format.

When converting statements:
1. Use the SQL tools to understand PostgreSQL syntax requirements
2. Apply PostgreSQL-specific functions and operators
3. Ensure proper data type handling
4. Follow PostgreSQL best practices
5. Provide detailed explanations of changes made

Available tools: {tool_names}
"""),
                    ("human", "{input}"),
                    MessagesPlaceholder(variable_name="agent_scratchpad"),
                ])

                # Create the agent
                self.sql_agent = create_openai_tools_agent(
                    llm=self.llm,
                    tools=self.sql_tools,
                    prompt=sql_conversion_prompt
                )

                # Create agent executor
                self.agent_executor = AgentExecutor(
                    agent=self.sql_agent,
                    tools=self.sql_tools,
                    verbose=True,
                    handle_parsing_errors=True,
                    max_iterations=3
                )

                print("🤖 SQL Agent executor created successfully")

            else:
                print("⚠️ No SQL toolkit available, agent executor not created")
                self.agent_executor = None

        except Exception as e:
            print(f"⚠️ SQL agent setup failed: {e}")
            self.agent_executor = None

    def convert_with_toolkit(self, source_context, target_error_context, target_statements, error_message, previous_feedback=None):
        """
        Convert statements using SQL toolkit approach with same context as AI conversion.

        Args:
            source_context: ErrorContext with Oracle source statements
            target_error_context: ErrorContext with PostgreSQL target statements
            target_statements: List of all target statements for context
            error_message: Deployment error message
            previous_feedback: Previous conversion feedback

        Returns:
            Dictionary with corrected statements and explanations
        """
        try:
            print("🔧 Using SQL Toolkit conversion strategy...")

            # Check if we have the SQL agent executor
            if self.agent_executor:
                print("🤖 Using langchain SQL agent executor for conversion")
                return self._convert_with_sql_agent(
                    source_context, target_error_context, target_statements,
                    error_message, previous_feedback
                )
            else:
                print("📝 Using direct LLM approach with SQL expertise")
                return self._convert_with_direct_llm(
                    source_context, target_error_context, target_statements,
                    error_message, previous_feedback
                )

        except Exception as e:
            print(f"❌ SQL Toolkit conversion failed: {str(e)}")
            return None

    def _convert_with_sql_agent(self, source_context, target_error_context, target_statements, error_message, previous_feedback):
        """Convert using the SQL agent executor with toolkit tools."""
        try:
            # Create input for the SQL agent
            agent_input = self._create_agent_input(
                source_context, target_error_context, target_statements,
                error_message, previous_feedback
            )

            # Execute the SQL agent
            print("🤖 Executing SQL agent with toolkit tools...")
            agent_result = self.agent_executor.invoke({"input": agent_input})

            # Parse the agent result and convert to expected format
            return self._parse_agent_result(agent_result, target_error_context)

        except Exception as e:
            print(f"❌ SQL agent execution failed: {str(e)}")
            # Fallback to direct LLM approach
            return self._convert_with_direct_llm(
                source_context, target_error_context, target_statements,
                error_message, previous_feedback
            )

    def _convert_with_direct_llm(self, source_context, target_error_context, target_statements, error_message, previous_feedback):
        """Convert using direct LLM approach with SQL expertise."""
        try:
            # Create toolkit-specific prompt using same context structure
            toolkit_prompt = self._create_toolkit_conversion_prompt(
                source_context, target_error_context, target_statements,
                error_message, previous_feedback
            )

            # Use toolkit's PostgreSQL expertise for conversion
            conversion_result = self._execute_toolkit_conversion(toolkit_prompt)

            return {
                "ai_corrections": conversion_result.get("corrected_statements", []),
                "explanation": conversion_result.get("explanation", ""),
                "conversion_strategy": "sql_toolkit",
                "toolkit_reasoning": conversion_result.get("toolkit_reasoning", "")
            }

        except Exception as e:
            print(f"❌ Direct LLM conversion failed: {str(e)}")
            return None

    def _create_agent_input(self, source_context, target_error_context, target_statements, error_message, previous_feedback):
        """Create input for the SQL agent executor."""

        # Check if this is target-specific (no source mapping)
        is_target_specific = (source_context.error_statement_number == 0 or
                             not source_context.error_statement or
                             source_context.error_statement.strip() == "")

        feedback_section = ""
        if previous_feedback:
            feedback_section = f"\nPREVIOUS FEEDBACK: {previous_feedback}\nPlease incorporate this feedback."

        if is_target_specific:
            agent_input = f"""
I need help fixing a PostgreSQL syntax error. Please use your SQL tools to analyze and fix this error.

ERROR MESSAGE: {error_message}

TARGET STATEMENT WITH ERROR:
Statement #{target_error_context.error_statement_number}: {target_error_context.error_statement}

CONTEXT:
Before: {target_error_context.before_statement}
After: {target_error_context.after_statement}

{feedback_section}

Please:
1. Use SQL tools to understand the PostgreSQL syntax requirements
2. Analyze the error message to identify the specific issue
3. Provide the corrected PostgreSQL statement
4. Explain what was wrong and how you fixed it

Return the result in this JSON format:
{{
  "corrected_statement": "the fixed SQL statement",
  "explanation": "detailed explanation of the fix",
  "changes_made": "specific changes made to fix the error"
}}
"""
        else:
            agent_input = f"""
I need help converting an Oracle SQL statement to PostgreSQL and fixing a deployment error.

ERROR MESSAGE: {error_message}

ORACLE SOURCE STATEMENT:
{source_context.error_statement}

POSTGRESQL TARGET STATEMENT (with error):
{target_error_context.error_statement}

CONTEXT:
Oracle Before: {source_context.before_statement}
Oracle After: {source_context.after_statement}
PostgreSQL Before: {target_error_context.before_statement}
PostgreSQL After: {target_error_context.after_statement}

{feedback_section}

Please:
1. Use SQL tools to understand PostgreSQL syntax and functions
2. Compare the Oracle and PostgreSQL statements to understand the conversion intent
3. Fix the PostgreSQL statement to work correctly while maintaining the Oracle logic
4. Explain the conversion and fixes applied

Return the result in this JSON format:
{{
  "corrected_statement": "the fixed PostgreSQL statement",
  "explanation": "detailed explanation of the conversion and fix",
  "changes_made": "specific changes made for PostgreSQL compatibility"
}}
"""

        return agent_input

    def _parse_agent_result(self, agent_result, target_error_context):
        """Parse the SQL agent result and convert to expected format."""
        try:
            # Extract the output from agent result
            output = agent_result.get("output", "")

            # Try to extract JSON from the output
            import json
            import re

            # Look for JSON in the output
            json_match = re.search(r'\{[^}]*"corrected_statement"[^}]*\}', output, re.DOTALL)

            if json_match:
                try:
                    parsed_json = json.loads(json_match.group())
                    corrected_statement = parsed_json.get("corrected_statement", "")
                    explanation = parsed_json.get("explanation", "")
                    changes_made = parsed_json.get("changes_made", "")

                    # Convert to expected format
                    corrected_statements = [{
                        "statement_number": target_error_context.error_statement_number,
                        "original_statement": target_error_context.error_statement,
                        "corrected_statement": corrected_statement,
                        "statement_type": "error_statement",
                        "changes_made": changes_made
                    }]

                    return {
                        "ai_corrections": corrected_statements,
                        "explanation": explanation,
                        "conversion_strategy": "sql_toolkit",
                        "toolkit_reasoning": "Used langchain SQL agent with toolkit tools"
                    }

                except json.JSONDecodeError:
                    pass

            # If JSON parsing fails, create a basic response
            return {
                "ai_corrections": [{
                    "statement_number": target_error_context.error_statement_number,
                    "original_statement": target_error_context.error_statement,
                    "corrected_statement": target_error_context.error_statement,  # No change if parsing failed
                    "statement_type": "error_statement",
                    "changes_made": "SQL agent processing completed but result parsing failed"
                }],
                "explanation": f"SQL agent output: {output}",
                "conversion_strategy": "sql_toolkit",
                "toolkit_reasoning": "Used langchain SQL agent but result parsing failed"
            }

        except Exception as e:
            print(f"❌ Agent result parsing failed: {str(e)}")
            return {
                "ai_corrections": [],
                "explanation": f"Agent result parsing failed: {str(e)}",
                "conversion_strategy": "sql_toolkit",
                "toolkit_reasoning": "Agent execution failed"
            }

    def _create_toolkit_conversion_prompt(self, source_context, target_error_context, target_statements, error_message, previous_feedback):
        """Create conversion prompt optimized for SQL toolkit approach."""

        # Check if this is target-specific (no source mapping)
        is_target_specific = (source_context.error_statement_number == 0 or
                             not source_context.error_statement or
                             source_context.error_statement.strip() == "")

        feedback_section = ""
        if previous_feedback:
            feedback_section = f"""
PREVIOUS CONVERSION FEEDBACK:
{previous_feedback}

INCORPORATE THIS FEEDBACK INTO THE NEW CONVERSION.
"""

        if is_target_specific:
            return f"""
You are a PostgreSQL Database Expert with deep knowledge of PostgreSQL syntax, functions, and best practices.

TASK: Fix the PostgreSQL syntax error in the target statement using your PostgreSQL expertise.

{feedback_section}

ERROR MESSAGE:
{error_message}

TARGET CONTEXT (PostgreSQL with error):
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

COMPLETE TARGET CODE CONTEXT:
{chr(10).join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)]) if target_statements else "Limited context available"}

POSTGRESQL EXPERT ANALYSIS REQUIRED:
1. Analyze the PostgreSQL error message to understand the specific syntax violation
2. Apply PostgreSQL-specific knowledge to fix the syntax error
3. Ensure the corrected statement follows PostgreSQL best practices
4. Provide detailed explanation of PostgreSQL-specific changes made

Use your PostgreSQL expertise to provide the corrected statements in the same JSON format as expected.
"""
        else:
            return f"""
You are a PostgreSQL Database Expert specializing in Oracle to PostgreSQL migrations.

TASK: Convert the Oracle source statement to correct PostgreSQL syntax, fixing the deployment error.

{feedback_section}

ERROR MESSAGE:
{error_message}

SOURCE CONTEXT (Oracle):
Before Error (#{source_context.before_statement_number}):
{source_context.before_statement}

Error Statement (#{source_context.error_statement_number}):
{source_context.error_statement}

After Error (#{source_context.after_statement_number}):
{source_context.after_statement}

TARGET CONTEXT (PostgreSQL with error):
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

COMPLETE TARGET CODE CONTEXT:
{chr(10).join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)]) if target_statements else "Limited context available"}

POSTGRESQL MIGRATION EXPERT ANALYSIS:
1. Compare Oracle source with PostgreSQL target to understand the conversion intent
2. Identify what went wrong in the Oracle to PostgreSQL conversion
3. Apply correct PostgreSQL syntax and functions to fix the error
4. Ensure the PostgreSQL statement maintains the same business logic as Oracle
5. Use PostgreSQL best practices and optimal syntax

Provide corrected statements in the expected JSON format with detailed PostgreSQL-specific explanations.
"""

    def _execute_toolkit_conversion(self, prompt):
        """Execute the conversion using toolkit's PostgreSQL expertise."""
        try:
            # Use the LLM with structured output for consistency with AI approach
            from state import StatementConversionOutput

            structured_llm = self.llm.client.with_structured_output(StatementConversionOutput)
            result = structured_llm.invoke(prompt)

            # Convert to expected format
            corrected_statements = []
            for stmt in result.corrected_statements:
                corrected_statements.append({
                    "statement_number": stmt.statement_number,
                    "original_statement": stmt.original_statement,
                    "corrected_statement": stmt.corrected_statement,
                    "statement_type": stmt.statement_type,
                    "changes_made": stmt.changes_made
                })

            return {
                "corrected_statements": corrected_statements,
                "explanation": result.explanation,
                "toolkit_reasoning": "Used PostgreSQL expertise for conversion"
            }

        except Exception as e:
            print(f"❌ Toolkit conversion execution failed: {str(e)}")
            return {
                "corrected_statements": [],
                "explanation": f"Toolkit conversion failed: {str(e)}",
                "toolkit_reasoning": "Conversion failed"
            }
